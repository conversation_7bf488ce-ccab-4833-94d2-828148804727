<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="http://xmlns.jcp.org/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee http://xmlns.jcp.org/xml/ns/javaee/web-app_4_0.xsd"
         version="4.0">

    <display-name>My Webapp</display-name>

    <welcome-file-list>
        <welcome-file>index.html</welcome-file>
    </welcome-file-list>

    <!-- Registration Servlet Configuration -->
    <servlet>
        <servlet-name>RegistrationServlet</servlet-name>
        <servlet-class>com.example.servlet.RegistrationServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>RegistrationServlet</servlet-name>
        <url-pattern>/register</url-pattern>
    </servlet-mapping>

    <!-- Login Servlet Configuration -->
    <servlet>
        <servlet-name>LoginServlet</servlet-name>
        <servlet-class>com.example.servlet.LoginServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>LoginServlet</servlet-name>
        <url-pattern>/login</url-pattern>
    </servlet-mapping>

    <!-- Logout Servlet Configuration -->
    <servlet>
        <servlet-name>LogoutServlet</servlet-name>
        <servlet-class>com.example.servlet.LogoutServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>LogoutServlet</servlet-name>
        <url-pattern>/logout</url-pattern>
    </servlet-mapping>

    <!-- Authentication Filter Configuration -->
    <filter>
        <filter-name>AuthenticationFilter</filter-name>
        <filter-class>com.example.filter.AuthenticationFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>AuthenticationFilter</filter-name>
        <url-pattern>/dashboard.html</url-pattern>
    </filter-mapping>
    <filter-mapping>
        <filter-name>AuthenticationFilter</filter-name>
        <url-pattern>/api/*</url-pattern> <!-- Protecting future API endpoints -->
    </filter-mapping>

</web-app>

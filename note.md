<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CodeGram - LeetCode meets Instagram</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        :root {
            --primary: #6366f1;
            --secondary: #8b5cf6;
            --dark: #1e293b;
            --light: #f8fafc;
            --success: #10b981;
            --danger: #ef4444;
        }
        
        * {
            font-family: 'Inter', sans-serif;
            transition: all 0.3s ease;
        }
        
        body {
            background-color: #f8fafc;
            color: #1e293b;
        }
        
        .story-ring {
            background: linear-gradient(45deg, #6366f1, #8b5cf6);
        }
        
        .post-card {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        
        .code-editor {
            background-color: #1e293b;
            color: #f8fafc;
            font-family: 'Courier New', Courier, monospace;
        }
        
        .tab-active {
            border-bottom: 2px solid var(--primary);
            color: var(--primary);
        }
        
        .modal {
            animation: fadeIn 0.3s ease;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .story-viewer {
            animation: slideIn 0.3s ease;
        }
        
        @keyframes slideIn {
            from { transform: translateX(100%); }
            to { transform: translateX(0); }
        }
        
        .channel-message {
            animation: messageIn 0.2s ease;
        }
        
        @keyframes messageIn {
            from { opacity: 0; transform: translateY(5px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .like-animation {
            animation: like 0.5s ease;
        }
        
        @keyframes like {
            0% { transform: scale(1); }
            50% { transform: scale(1.3); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body class="min-h-screen">
    <!-- Navigation -->
    <nav class="fixed bottom-0 md:bottom-auto md:top-0 w-full bg-white shadow-sm z-50">
        <div class="max-w-6xl mx-auto px-4">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <h1 class="text-xl font-bold text-indigo-600">CodeGram</h1>
                </div>
                
                <!-- Mobile menu button -->
                <div class="md:hidden flex items-center">
                    <button id="mobile-menu-button" class="text-gray-500 hover:text-gray-900">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
                
                <!-- Desktop menu -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#" class="text-gray-900 hover:text-indigo-600">
                        <i class="fas fa-home text-xl"></i>
                    </a>
                    <a href="#" class="text-gray-500 hover:text-indigo-600">
                        <i class="fas fa-search text-xl"></i>
                    </a>
                    <a href="#" class="text-gray-500 hover:text-indigo-600">
                        <i class="fas fa-plus-square text-xl"></i>
                    </a>
                    <a href="#" class="text-gray-500 hover:text-indigo-600">
                        <i class="fas fa-heart text-xl"></i>
                    </a>
                    <div class="relative">
                        <img id="profile-menu-button" src="https://randomuser.me/api/portraits/women/44.jpg" alt="Profile" class="w-8 h-8 rounded-full cursor-pointer">
                        <!-- Profile dropdown -->
                        <div id="profile-dropdown" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Profile</a>
                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Saved</a>
                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Settings</a>
                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Log out</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Mobile menu -->
        <div id="mobile-menu" class="hidden md:hidden bg-white border-t">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 flex justify-around">
                <a href="#" class="text-gray-900 hover:text-indigo-600 block px-3 py-2">
                    <i class="fas fa-home text-xl"></i>
                </a>
                <a href="#" class="text-gray-500 hover:text-indigo-600 block px-3 py-2">
                    <i class="fas fa-search text-xl"></i>
                </a>
                <a href="#" class="text-gray-500 hover:text-indigo-600 block px-3 py-2">
                    <i class="fas fa-plus-square text-xl"></i>
                </a>
                <a href="#" class="text-gray-500 hover:text-indigo-600 block px-3 py-2">
                    <i class="fas fa-heart text-xl"></i>
                </a>
                <a href="#" class="text-gray-500 hover:text-indigo-600 block px-3 py-2">
                    <i class="fas fa-user text-xl"></i>
                </a>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="pt-16 pb-20 md:pb-0">
        <!-- Stories -->
        <div class="max-w-2xl mx-auto px-4 py-4 overflow-x-auto">
            <div class="flex space-x-4">
                <!-- Your story -->
                <div class="flex flex-col items-center space-y-1">
                    <div class="relative">
                        <div class="w-16 h-16 rounded-full flex items-center justify-center story-ring p-0.5">
                            <div class="w-full h-full rounded-full bg-white flex items-center justify-center">
                                <i class="fas fa-plus text-indigo-600"></i>
                            </div>
                        </div>
                    </div>
                    <span class="text-xs">Your Story</span>
                </div>
                
                <!-- Other stories -->
                <div class="flex flex-col items-center space-y-1">
                    <div class="relative">
                        <div class="w-16 h-16 rounded-full flex items-center justify-center story-ring p-0.5">
                            <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User" class="w-full h-full rounded-full object-cover">
                        </div>
                        <div class="absolute bottom-0 right-0 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
                    </div>
                    <span class="text-xs">john_doe</span>
                </div>
                
                <div class="flex flex-col items-center space-y-1">
                    <div class="relative">
                        <div class="w-16 h-16 rounded-full flex items-center justify-center story-ring p-0.5">
                            <img src="https://randomuser.me/api/portraits/women/12.jpg" alt="User" class="w-full h-full rounded-full object-cover">
                        </div>
                    </div>
                    <span class="text-xs">alice_codes</span>
                </div>
                
                <div class="flex flex-col items-center space-y-1">
                    <div class="relative">
                        <div class="w-16 h-16 rounded-full flex items-center justify-center story-ring p-0.5">
                            <img src="https://randomuser.me/api/portraits/men/75.jpg" alt="User" class="w-full h-full rounded-full object-cover">
                        </div>
                        <div class="absolute bottom-0 right-0 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
                    </div>
                    <span class="text-xs">dev_master</span>
                </div>
                
                <div class="flex flex-col items-center space-y-1">
                    <div class="relative">
                        <div class="w-16 h-16 rounded-full flex items-center justify-center story-ring p-0.5">
                            <img src="https://randomuser.me/api/portraits/women/33.jpg" alt="User" class="w-full h-full rounded-full object-cover">
                        </div>
                    </div>
                    <span class="text-xs">python_lover</span>
                </div>
                
                <div class="flex flex-col items-center space-y-1">
                    <div class="relative">
                        <div class="w-16 h-16 rounded-full flex items-center justify-center story-ring p-0.5">
                            <img src="https://randomuser.me/api/portraits/men/44.jpg" alt="User" class="w-full h-full rounded-full object-cover">
                        </div>
                        <div class="absolute bottom-0 right-0 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
                    </div>
                    <span class="text-xs">js_wizard</span>
                </div>
                
                <div class="flex flex-col items-center space-y-1">
                    <div class="relative">
                        <div class="w-16 h-16 rounded-full flex items-center justify-center story-ring p-0.5">
                            <img src="https://randomuser.me/api/portraits/women/55.jpg" alt="User" class="w-full h-full rounded-full object-cover">
                        </div>
                    </div>
                    <span class="text-xs">algo_queen</span>
                </div>
            </div>
        </div>
        
        <!-- Posts -->
        <div class="max-w-2xl mx-auto">
            <!-- Post 1 -->
            <div class="bg-white post-card rounded-md mb-6">
                <!-- Post header -->
                <div class="flex items-center justify-between p-4">
                    <div class="flex items-center space-x-3">
                        <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User" class="w-8 h-8 rounded-full">
                        <span class="font-semibold">john_doe</span>
                    </div>
                    <button class="text-gray-500 hover:text-gray-900">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                </div>
                
                <!-- Post content -->
                <div class="relative">
                    <div class="w-full h-96 bg-gray-100 flex items-center justify-center">
                        <div class="code-editor p-4 rounded-md w-full h-full overflow-auto">
                            <div class="flex items-center mb-4">
                                <span class="bg-red-500 w-3 h-3 rounded-full mr-2"></span>
                                <span class="bg-yellow-500 w-3 h-3 rounded-full mr-2"></span>
                                <span class="bg-green-500 w-3 h-3 rounded-full mr-2"></span>
                                <span class="text-sm ml-2">two_sum.py</span>
                            </div>
                            <pre class="text-sm"><code>def twoSum(nums, target):
    hashmap = {}
    for i, num in enumerate(nums):
        complement = target - num
        if complement in hashmap:
            return [hashmap[complement], i]
        hashmap[num] = i
    return []</code></pre>
                        </div>
                    </div>
                </div>
                
                <!-- Post actions -->
                <div class="p-4">
                    <div class="flex justify-between mb-2">
                        <div class="flex space-x-4">
                            <button class="like-btn text-gray-500 hover:text-red-500">
                                <i class="far fa-heart text-xl"></i>
                            </button>
                            <button class="text-gray-500 hover:text-gray-900">
                                <i class="far fa-comment text-xl"></i>
                            </button>
                            <button class="text-gray-500 hover:text-gray-900">
                                <i class="far fa-paper-plane text-xl"></i>
                            </button>
                        </div>
                        <button class="text-gray-500 hover:text-gray-900">
                            <i class="far fa-bookmark text-xl"></i>
                        </button>
                    </div>
                    
                    <!-- Likes -->
                    <div class="font-semibold mb-1">1,234 likes</div>
                    
                    <!-- Caption -->
                    <div class="mb-1">
                        <span class="font-semibold">john_doe</span> 
                        <span>Solution for Two Sum problem on LeetCode. Time complexity O(n), space complexity O(n). #leetcode #algorithms #python</span>
                    </div>
                    
                    <!-- Comments -->
                    <div class="text-gray-500 mb-1">View all 56 comments</div>
                    <div class="flex items-center">
                        <input type="text" placeholder="Add a comment..." class="w-full py-2 px-1 focus:outline-none">
                        <button class="text-indigo-600 font-semibold">Post</button>
                    </div>
                </div>
            </div>
            
            <!-- Post 2 -->
            <div class="bg-white post-card rounded-md mb-6">
                <!-- Post header -->
                <div class="flex items-center justify-between p-4">
                    <div class="flex items-center space-x-3">
                        <img src="https://randomuser.me/api/portraits/women/12.jpg" alt="User" class="w-8 h-8 rounded-full">
                        <span class="font-semibold">alice_codes</span>
                    </div>
                    <button class="text-gray-500 hover:text-gray-900">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                </div>
                
                <!-- Post content -->
                <div class="relative">
                    <div class="w-full h-96 bg-gray-100 flex items-center justify-center">
                        <div class="w-full h-full p-4">
                            <div class="bg-white rounded-md shadow-md p-4 h-full overflow-auto">
                                <h3 class="text-lg font-bold mb-2">Binary Tree Traversal Cheat Sheet</h3>
                                <div class="mb-4">
                                    <h4 class="font-semibold mb-1">Pre-order (Root, Left, Right):</h4>
                                    <pre class="code-editor p-2 rounded text-sm"><code>def preorder(root):
    if root:
        print(root.val)
        preorder(root.left)
        preorder(root.right)</code></pre>
                                </div>
                                <div class="mb-4">
                                    <h4 class="font-semibold mb-1">In-order (Left, Root, Right):</h4>
                                    <pre class="code-editor p-2 rounded text-sm"><code>def inorder(root):
    if root:
        inorder(root.left)
        print(root.val)
        inorder(root.right)</code></pre>
                                </div>
                                <div>
                                    <h4 class="font-semibold mb-1">Post-order (Left, Right, Root):</h4>
                                    <pre class="code-editor p-2 rounded text-sm"><code>def postorder(root):
    if root:
        postorder(root.left)
        postorder(root.right)
        print(root.val)</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Post actions -->
                <div class="p-4">
                    <div class="flex justify-between mb-2">
                        <div class="flex space-x-4">
                            <button class="like-btn text-gray-500 hover:text-red-500">
                                <i class="far fa-heart text-xl"></i>
                            </button>
                            <button class="text-gray-500 hover:text-gray-900">
                                <i class="far fa-comment text-xl"></i>
                            </button>
                            <button class="text-gray-500 hover:text-gray-900">
                                <i class="far fa-paper-plane text-xl"></i>
                            </button>
                        </div>
                        <button class="text-gray-500 hover:text-gray-900">
                            <i class="far fa-bookmark text-xl"></i>
                        </button>
                    </div>
                    
                    <!-- Likes -->
                    <div class="font-semibold mb-1">892 likes</div>
                    
                    <!-- Caption -->
                    <div class="mb-1">
                        <span class="font-semibold">alice_codes</span> 
                        <span>Binary tree traversal methods cheat sheet. Useful for coding interviews! #datastructures #binarytree #codinginterview</span>
                    </div>
                    
                    <!-- Comments -->
                    <div class="text-gray-500 mb-1">View all 32 comments</div>
                    <div class="flex items-center">
                        <input type="text" placeholder="Add a comment..." class="w-full py-2 px-1 focus:outline-none">
                        <button class="text-indigo-600 font-semibold">Post</button>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Story Viewer Modal -->
    <div id="story-modal" class="hidden fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center">
        <div class="story-viewer relative w-full h-full max-w-md max-h-screen">
            <div class="absolute top-4 left-4 right-4 flex justify-between items-center z-10">
                <div class="flex items-center space-x-2">
                    <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User" class="w-8 h-8 rounded-full">
                    <span class="text-white font-semibold">john_doe</span>
                </div>
                <button id="close-story" class="text-white text-xl">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="w-full h-full flex items-center justify-center">
                <div class="code-editor p-4 rounded-md w-full h-96 mx-4 overflow-auto">
                    <div class="flex items-center mb-4">
                        <span class="bg-red-500 w-3 h-3 rounded-full mr-2"></span>
                        <span class="bg-yellow-500 w-3 h-3 rounded-full mr-2"></span>
                        <span class="bg-green-500 w-3 h-3 rounded-full mr-2"></span>
                        <span class="text-sm ml-2">merge_sort.js</span>
                    </div>
                    <pre class="text-sm"><code>function mergeSort(arr) {
    if (arr.length <= 1) return arr;
    
    const mid = Math.floor(arr.length / 2);
    const left = mergeSort(arr.slice(0, mid));
    const right = mergeSort(arr.slice(mid));
    
    return merge(left, right);
}

function merge(left, right) {
    let result = [];
    let i = 0, j = 0;
    
    while (i < left.length && j < right.length) {
        if (left[i] < right[j]) {
            result.push(left[i]);
            i++;
        } else {
            result.push(right[j]);
            j++;
        }
    }
    
    return result.concat(left.slice(i)).concat(right.slice(j));
}</code></pre>
                </div>
            </div>
            
            <div class="absolute bottom-4 left-4 right-4 z-10">
                <div class="flex items-center space-x-4">
                    <input type="text" placeholder="Send message" class="flex-1 bg-gray-800 text-white px-4 py-2 rounded-full focus:outline-none">
                    <button class="text-white">
                        <i class="far fa-heart text-xl"></i>
                    </button>
                    <button class="text-white">
                        <i class="far fa-paper-plane text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Create Post Modal -->
    <div id="create-post-modal" class="hidden fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center">
        <div class="modal bg-white rounded-lg w-full max-w-md max-h-screen overflow-hidden">
            <div class="border-b p-4 flex justify-between items-center">
                <h2 class="text-lg font-semibold">Create New Post</h2>
                <button id="close-create-post" class="text-gray-500 hover:text-gray-900">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="p-4">
                <div class="flex border-b mb-4">
                    <button class="tab-btn px-4 py-2 font-medium text-gray-500 tab-active" data-tab="code">Code</button>
                    <button class="tab-btn px-4 py-2 font-medium text-gray-500" data-tab="document">Document</button>
                    <button class="tab-btn px-4 py-2 font-medium text-gray-500" data-tab="channel">Channel</button>
                </div>
                
                <!-- Code Tab -->
                <div id="code-tab" class="tab-content">
                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2">Problem Title</label>
                        <input type="text" class="w-full px-3 py-2 border rounded focus:outline-none focus:ring-1 focus:ring-indigo-500" placeholder="Two Sum">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2">Language</label>
                        <select class="w-full px-3 py-2 border rounded focus:outline-none focus:ring-1 focus:ring-indigo-500">
                            <option>Python</option>
                            <option>JavaScript</option>
                            <option>Java</option>
                            <option>C++</option>
                            <option>C#</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2">Your Solution</label>
                        <div class="code-editor p-4 rounded-md h-64 overflow-auto">
                            <div class="flex items-center mb-4">
                                <span class="bg-red-500 w-3 h-3 rounded-full mr-2"></span>
                                <span class="bg-yellow-500 w-3 h-3 rounded-full mr-2"></span>
                                <span class="bg-green-500 w-3 h-3 rounded-full mr-2"></span>
                                <span class="text-sm ml-2">solution.py</span>
                            </div>
                            <textarea class="w-full h-40 bg-transparent text-white font-mono focus:outline-none" placeholder="Write your code here..."></textarea>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2">Caption</label>
                        <textarea class="w-full px-3 py-2 border rounded focus:outline-none focus:ring-1 focus:ring-indigo-500" placeholder="Explain your solution..."></textarea>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2">Tags</label>
                        <input type="text" class="w-full px-3 py-2 border rounded focus:outline-none focus:ring-1 focus:ring-indigo-500" placeholder="#leetcode #algorithms #python">
                    </div>
                    
                    <button class="w-full bg-indigo-600 text-white py-2 rounded-md hover:bg-indigo-700">Post</button>
                </div>
                
                <!-- Document Tab -->
                <div id="document-tab" class="tab-content hidden">
                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2">Document Title</label>
                        <input type="text" class="w-full px-3 py-2 border rounded focus:outline-none focus:ring-1 focus:ring-indigo-500" placeholder="System Design Cheat Sheet">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2">Document Type</label>
                        <select class="w-full px-3 py-2 border rounded focus:outline-none focus:ring-1 focus:ring-indigo-500">
                            <option>PDF</option>
                            <option>Word</option>
                            <option>Markdown</option>
                            <option>Text</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2">Upload Document</label>
                        <div class="border-2 border-dashed border-gray-300 rounded-md p-8 text-center">
                            <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-2"></i>
                            <p class="text-gray-500">Drag and drop your file here or click to browse</p>
                            <input type="file" class="hidden">
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2">Description</label>
                        <textarea class="w-full px-3 py-2 border rounded focus:outline-none focus:ring-1 focus:ring-indigo-500" placeholder="Describe your document..."></textarea>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2">Tags</label>
                        <input type="text" class="w-full px-3 py-2 border rounded focus:outline-none focus:ring-1 focus:ring-indigo-500" placeholder="#systemdesign #interview #cheatsheet">
                    </div>
                    
                    <button class="w-full bg-indigo-600 text-white py-2 rounded-md hover:bg-indigo-700">Post</button>
                </div>
                
                <!-- Channel Tab -->
                <div id="channel-tab" class="tab-content hidden">
                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2">Channel Name</label>
                        <input type="text" class="w-full px-3 py-2 border rounded focus:outline-none focus:ring-1 focus:ring-indigo-500" placeholder="Python Learners">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2">Channel Description</label>
                        <textarea class="w-full px-3 py-2 border rounded focus:outline-none focus:ring-1 focus:ring-indigo-500" placeholder="Describe your channel..."></textarea>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2">Channel Type</label>
                        <select class="w-full px-3 py-2 border rounded focus:outline-none focus:ring-1 focus:ring-indigo-500">
                            <option>Public (Anyone can join)</option>
                            <option>Private (Invite only)</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2">Channel Photo</label>
                        <div class="border-2 border-dashed border-gray-300 rounded-md p-8 text-center">
                            <i class="fas fa-camera text-4xl text-gray-400 mb-2"></i>
                            <p class="text-gray-500">Upload a channel photo</p>
                            <input type="file" class="hidden">
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2">Topics</label>
                        <input type="text" class="w-full px-3 py-2 border rounded focus:outline-none focus:ring-1 focus:ring-indigo-500" placeholder="#python #beginners #learning">
                    </div>
                    
                    <button class="w-full bg-indigo-600 text-white py-2 rounded-md hover:bg-indigo-700">Create Channel</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Channel View Modal -->
    <div id="channel-modal" class="hidden fixed inset-0 bg-white z-50">
        <div class="h-full flex flex-col">
            <!-- Channel Header -->
            <div class="border-b p-4 flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button id="back-to-posts" class="text-gray-500 hover:text-gray-900">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <div class="flex items-center space-x-2">
                        <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="Channel" class="w-8 h-8 rounded-full">
                        <div>
                            <h3 class="font-semibold">Python Learners</h3>
                            <p class="text-xs text-gray-500">245 members</p>
                        </div>
                    </div>
                </div>
                <button class="text-gray-500 hover:text-gray-900">
                    <i class="fas fa-ellipsis-h"></i>
                </button>
            </div>
            
            <!-- Channel Messages -->
            <div class="flex-1 overflow-y-auto p-4 space-y-4">
                <!-- Message 1 -->
                <div class="channel-message flex items-start space-x-2">
                    <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User" class="w-8 h-8 rounded-full">
                    <div>
                        <div class="bg-gray-100 rounded-lg p-3 max-w-xs">
                            <p>Hey everyone! I'm stuck on this Python problem. Can anyone help?</p>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">john_doe · 2 min ago</p>
                    </div>
                </div>
                
                <!-- Message 2 -->
                <div class="channel-message flex items-start space-x-2 justify-end">
                    <div class="text-right">
                        <div class="bg-indigo-100 rounded-lg p-3 max-w-xs ml-auto">
                            <p>What's the problem? Maybe I can help.</p>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">You · 1 min ago</p>
                    </div>
                    <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="User" class="w-8 h-8 rounded-full">
                </div>
                
                <!-- Message 3 -->
                <div class="channel-message flex items-start space-x-2">
                    <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User" class="w-8 h-8 rounded-full">
                    <div>
                        <div class="bg-gray-100 rounded-lg p-3 max-w-xs">
                            <p>I'm trying to write a function that reverses a string without using [::-1] or reversed(). Any ideas?</p>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">john_doe · Just now</p>
                    </div>
                </div>
                
                <!-- Code snippet message -->
                <div class="channel-message flex items-start space-x-2">
                    <img src="https://randomuser.me/api/portraits/women/12.jpg" alt="User" class="w-8 h-8 rounded-full">
                    <div>
                        <div class="code-editor rounded-lg p-3 max-w-xs">
                            <pre class="text-sm"><code>def reverse_string(s):
    result = []
    for i in range(len(s)-1, -1, -1):
        result.append(s[i])
    return ''.join(result)</code></pre>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">alice_codes · Just now</p>
                    </div>
                </div>
            </div>
            
            <!-- Message Input -->
            <div class="border-t p-4">
                <div class="flex items-center space-x-2">
                    <button class="text-gray-500 hover:text-gray-900">
                        <i class="fas fa-plus"></i>
                    </button>
                    <input type="text" placeholder="Type a message..." class="flex-1 border rounded-full py-2 px-4 focus:outline-none focus:ring-1 focus:ring-indigo-500">
                    <button class="text-gray-500 hover:text-gray-900">
                        <i class="far fa-smile"></i>
                    </button>
                    <button class="text-indigo-600">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            document.getElementById('mobile-menu').classList.toggle('hidden');
        });
        
        // Profile dropdown toggle
        document.getElementById('profile-menu-button').addEventListener('click', function() {
            document.getElementById('profile-dropdown').classList.toggle('hidden');
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('#profile-menu-button') && !event.target.closest('#profile-dropdown')) {
                document.getElementById('profile-dropdown').classList.add('hidden');
            }
        });
        
        // Story viewer
        const storyElements = document.querySelectorAll('.story-ring');
        storyElements.forEach(story => {
            story.addEventListener('click', function() {
                document.getElementById('story-modal').classList.remove('hidden');
            });
        });
        
        // Close story viewer
        document.getElementById('close-story').addEventListener('click', function() {
            document.getElementById('story-modal').classList.add('hidden');
        });
        
        // Like button animation
        const likeButtons = document.querySelectorAll('.like-btn');
        likeButtons.forEach(button => {
            button.addEventListener('click', function() {
                const icon = this.querySelector('i');
                icon.classList.toggle('far');
                icon.classList.toggle('fas');
                icon.classList.toggle('text-red-500');
                icon.classList.add('like-animation');
                
                setTimeout(() => {
                    icon.classList.remove('like-animation');
                }, 500);
            });
        });
        
        // Create post modal
        document.querySelectorAll('[href*="plus-square"]').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                document.getElementById('create-post-modal').classList.remove('hidden');
            });
        });
        
        // Close create post modal
        document.getElementById('close-create-post').addEventListener('click', function() {
            document.getElementById('create-post-modal').classList.add('hidden');
        });
        
        // Tab switching in create post modal
        const tabButtons = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');
        
        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Remove active class from all buttons
                tabButtons.forEach(btn => btn.classList.remove('tab-active'));
                // Add active class to clicked button
                this.classList.add('tab-active');
                
                // Hide all tab contents
                tabContents.forEach(content => content.classList.add('hidden'));
                // Show the selected tab content
                const tabId = this.getAttribute('data-tab') + '-tab';
                document.getElementById(tabId).classList.remove('hidden');
            });
        });
        
        // Channel view
        document.querySelectorAll('[href*="channel"]').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                document.getElementById('channel-modal').classList.remove('hidden');
            });
        });
        
        // Back to posts from channel
        document.getElementById('back-to-posts').addEventListener('click', function() {
            document.getElementById('channel-modal').classList.add('hidden');
        });
        
        // Simulate loading more posts when scrolling
        window.addEventListener('scroll', function() {
            if ((window.innerHeight + window.scrollY) >= document.body.offsetHeight - 500) {
                // In a real app, you would fetch more posts here
                console.log('Loading more posts...');
            }
        });
    </script>
</body>
</html>
<!DOCTYPE html>
<html>
<head>
    <title>Dashboard</title>
    <style>
        body {
            font-family: sans-serif;
            margin: 0;
            background-color: #f4f4f4;
        }
        .navbar {
            background-color: #333;
            overflow: hidden;
            padding: 10px 20px;
            color: white;
        }
        .navbar a {
            float: right;
            color: #f2f2f2;
            text-align: center;
            padding: 14px 16px;
            text-decoration: none;
            font-size: 17px;
        }
        .navbar a:hover {
            background-color: #ddd;
            color: black;
        }
        .navbar .welcome-message {
            float: left;
            padding: 14px 0; /* Align with links vertically */
            font-size: 18px;
        }
        .container {
            padding: 20px;
        }
        .content-section {
            background-color: #fff;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
        }
        h2 {
            color: #333;
        }
        p {
            color: #555;
        }
    </style>
</head>
<body>

<div class="navbar">
    <!-- Note: Displaying dynamic username requires server-side rendering (e.g., JSP, Thymeleaf)
         or JavaScript fetching user info after page load.
         For this pure HTML, we'll use a static message or placeholder. -->
    <span class="welcome-message">Welcome, User!</span>
    <a href="logout">Logout</a>
</div>

<div class="container">
    <div style="border: 1px solid #eee; margin: 10px; padding: 10px; background-color: #fff; border-radius: 8px; box-shadow: 0 0 10px rgba(0,0,0,0.05);">
        <h3>Today's Coding Challenge</h3>
        <p><em>(Details of the coding challenge will be loaded here dynamically in the future.)</em></p>
        <div style="background-color: #f9f9f9; padding: 8px; margin-top: 5px; border-radius: 4px;">
            <p><strong>Problem:</strong> FizzBuzz</p>
            <p><strong>Description:</strong> Write a program that prints numbers from 1 to 100. For multiples of three, print "Fizz" instead of the number, and for the multiples of five, print "Buzz". For numbers which are multiples of both three and five, print "FizzBuzz".</p>
            <textarea style="width: 90%; min-height: 50px; margin-top:5px; padding:5px; border: 1px solid #ccc; border-radius: 4px;" placeholder="Your solution here..."></textarea>
            <button style="margin-top:5px; padding: 8px 12px; background-color: #007bff; color:white; border:none; border-radius:4px; cursor:pointer;">Submit</button>
        </div>
    </div>

    <div style="border: 1px solid #eee; margin: 10px; padding: 10px; background-color: #fff; border-radius: 8px; box-shadow: 0 0 10px rgba(0,0,0,0.05);">
        <h3>Latest Educational Posts</h3>
        <p><em>(A list of educational posts with titles, summaries, and links/embedded content will appear here.)</em></p>
        <div style="background-color: #f9f9f9; padding: 8px; margin-top: 5px; border-radius: 4px;">
            <h4>Understanding Big O Notation</h4>
            <p>A quick guide to understanding algorithm efficiency...</p>
            <a href="#">Read more...</a>
        </div>
        <div style="background-color: #f9f9f9; padding: 8px; margin-top: 10px; border-radius: 4px;">
            <h4>Introduction to Cloud Functions</h4>
            <p>Learn how serverless functions can simplify your backend...</p>
            <a href="#">Watch video...</a>
        </div>
    </div>

    <div style="border: 1px solid #eee; margin: 10px; padding: 10px; background-color: #fff; border-radius: 8px; box-shadow: 0 0 10px rgba(0,0,0,0.05);">
        <h3>Upcoming Events & Quizzes</h3>
        <p><em>(Information about scheduled study events and quizzes.)</em></p>
         <div style="background-color: #f9f9f9; padding: 8px; margin-top: 5px; border-radius: 4px;">
            <p><strong>Quiz:</strong> Java Basics - March 15th, 2 PM UTC</p>
            <p><strong>Event:</strong> Group Study: DSA Q&A - March 18th, 4 PM UTC</p>
        </div>
    </div>
    
    <div style="border: 1px solid #eee; margin: 10px; padding: 10px; background-color: #fff; border-radius: 8px; box-shadow: 0 0 10px rgba(0,0,0,0.05);">
        <h3>Post a Poll</h3>
        <p><em>(Functionality to create and view polls will be here.)</em></p>
        <div style="background-color: #f9f9f9; padding: 8px; margin-top: 5px; border-radius: 4px;">
            <label for="poll-question">Poll Question:</label>
            <input type="text" id="poll-question" name="poll-question" style="width: 80%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
            <button style="margin-left:5px; padding: 8px 12px; background-color: #28a745; color:white; border:none; border-radius:4px; cursor:pointer;">Create Poll</button>
        </div>
    </div>

    <div style="border: 1px solid #eee; margin: 10px; padding: 10px; background-color: #fff; border-radius: 8px; box-shadow: 0 0 10px rgba(0,0,0,0.05);">
        <h3>Create an Event</h3>
        <p><em>(Interface to create study events.)</em></p>
        <div style="background-color: #f9f9f9; padding: 8px; margin-top: 5px; border-radius: 4px;">
            <label for="event-title">Event Title:</label>
            <input type="text" id="event-title" name="event-title" style="width: 80%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
            <button style="margin-left:5px; padding: 8px 12px; background-color: #17a2b8; color:white; border:none; border-radius:4px; cursor:pointer;">Create Event</button>
        </div>
    </div>
</div>

</body>
</html>

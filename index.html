<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Xercyaid - Learning Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#1e40af',
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-primary">Xercyaid</h1>
                    <span class="ml-2 text-gray-600">Learning Platform</span>
                </div>
                <div class="flex space-x-4">
                    <a href="login.html" class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-secondary transition duration-300">Login</a>
                    <a href="register.html" class="bg-gray-200 text-gray-800 px-4 py-2 rounded-lg hover:bg-gray-300 transition duration-300">Register</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <div class="bg-gradient-to-r from-primary to-secondary text-white">
        <div class="max-w-7xl mx-auto px-4 py-20">
            <div class="text-center">
                <h2 class="text-5xl font-bold mb-6">Welcome to Xercyaid</h2>
                <p class="text-xl mb-8 max-w-2xl mx-auto">Your comprehensive learning platform for coding challenges, educational content, and collaborative study sessions.</p>
                <div class="flex justify-center space-x-4">
                    <a href="register.html" class="bg-white text-primary px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition duration-300">Get Started</a>
                    <a href="#features" class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary transition duration-300">Learn More</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Features Section -->
    <div id="features" class="py-20">
        <div class="max-w-7xl mx-auto px-4">
            <div class="text-center mb-16">
                <h3 class="text-3xl font-bold text-gray-800 mb-4">Platform Features</h3>
                <p class="text-gray-600 max-w-2xl mx-auto">Discover all the tools and resources available to enhance your learning experience.</p>
            </div>
            
            <div class="grid md:grid-cols-3 gap-8">
                <!-- Coding Challenges -->
                <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition duration-300">
                    <div class="w-12 h-12 bg-primary rounded-lg flex items-center justify-center mb-6">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                        </svg>
                    </div>
                    <h4 class="text-xl font-semibold mb-4">Daily Coding Challenges</h4>
                    <p class="text-gray-600">Practice with curated coding problems designed to improve your programming skills and problem-solving abilities.</p>
                </div>

                <!-- Educational Content -->
                <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition duration-300">
                    <div class="w-12 h-12 bg-primary rounded-lg flex items-center justify-center mb-6">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                    <h4 class="text-xl font-semibold mb-4">Educational Resources</h4>
                    <p class="text-gray-600">Access comprehensive tutorials, articles, and videos covering various programming concepts and technologies.</p>
                </div>

                <!-- Community Events -->
                <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition duration-300">
                    <div class="w-12 h-12 bg-primary rounded-lg flex items-center justify-center mb-6">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                    <h4 class="text-xl font-semibold mb-4">Study Groups & Events</h4>
                    <p class="text-gray-600">Join collaborative study sessions, participate in quizzes, and engage with a community of learners.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-12">
        <div class="max-w-7xl mx-auto px-4">
            <div class="text-center">
                <h4 class="text-2xl font-bold mb-4">Xercyaid</h4>
                <p class="text-gray-400 mb-6">Empowering learners through interactive coding challenges and collaborative education.</p>
                <div class="flex justify-center space-x-6">
                    <a href="#" class="text-gray-400 hover:text-white transition duration-300">About</a>
                    <a href="#" class="text-gray-400 hover:text-white transition duration-300">Contact</a>
                    <a href="#" class="text-gray-400 hover:text-white transition duration-300">Privacy</a>
                    <a href="#" class="text-gray-400 hover:text-white transition duration-300">Terms</a>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>

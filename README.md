# Xercyaid Learning Platform

A modern, responsive learning platform built with HTML and Tailwind CSS.

## Features

- 🎯 **Daily Coding Challenges** - Practice with curated programming problems
- 📚 **Educational Resources** - Access tutorials, articles, and videos
- 👥 **Study Groups & Events** - Join collaborative learning sessions
- 📊 **Interactive Polls** - Create and participate in community polls
- 📅 **Event Management** - Organize and join study events
- 🎨 **Modern UI** - Clean, responsive design with Tailwind CSS

## Pages

- **Home** (`index.html`) - Landing page with platform overview
- **Login** (`login.html`) - User authentication
- **Register** (`register.html`) - New user registration
- **Dashboard** (`dashboard.html`) - Main user interface with all features

## Quick Start

### Option 1: Using Node.js (Recommended)
```bash
# Install dependencies
npm install

# Start development server with live reload
npm run dev

# Or start simple HTTP server
npm start
```

### Option 2: Using Python
```bash
# Start Python HTTP server
npm run serve
# Or directly: python -m http.server 8080
```

### Option 3: Direct Browser Access
Simply open `index.html` in your web browser.

## Development

The application uses:
- **HTML5** for structure
- **Tailwind CSS** (via CDN) for styling
- **Vanilla JavaScript** for interactivity
- **LocalStorage** for demo authentication

## Demo Credentials

Since this is a demo application, any username/password combination will work for login.

## Browser Support

Works in all modern browsers that support:
- ES6 JavaScript
- CSS Grid and Flexbox
- LocalStorage API

## License

MIT License - feel free to use this project as a starting point for your own learning platform!

# Codegram - Instagram meets LeetCode

A revolutionary social coding platform that combines the visual appeal of Instagram with the competitive programming features of LeetCode and HackerRank.

## 🚀 Concept

**Codegram** is where coding meets social media. Share your solutions, follow top programmers, compete in challenges, and build your coding reputation - all in an Instagram-like interface.

## ✨ Features

### 📱 **Instagram-Style Social Feed**
- Share code solutions with syntax highlighting
- Like, comment, and share coding posts
- Follow your favorite developers
- Stories feature for daily coding updates
- Beautiful, responsive mobile-first design

### 💻 **LeetCode-Style Problem Solving**
- 500+ coding challenges (Easy, Medium, Hard)
- Real-time code execution and testing
- Detailed problem descriptions and constraints
- Progress tracking and statistics
- Weekly contests and competitions

### 🏆 **Competitive Features**
- Global leaderboards and rankings
- Achievement badges and streaks
- Weekly coding contests
- Company-specific problem sets
- Interview preparation tracks

### 🎨 **Modern UI/UX**
- Instagram-inspired navigation and layout
- Syntax-highlighted code blocks
- Responsive design for all devices
- Dark/light theme support
- Smooth animations and transitions

## 📄 Pages

- **Home** (`index.html`) - Landing page showcasing the concept
- **Feed** (`feed.html`) - Instagram-style social coding feed
- **Problems** (`problems.html`) - LeetCode-style problem browser
- **Login** (`login.html`) - User authentication
- **Register** (`register.html`) - New user registration

## Quick Start

### Option 1: Using Node.js (Recommended)
```bash
# Install dependencies
npm install

# Start development server with live reload
npm run dev

# Or start simple HTTP server
npm start
```

### Option 2: Using Python
```bash
# Start Python HTTP server
npm run serve
# Or directly: python -m http.server 8080
```

### Option 3: Direct Browser Access
Simply open `index.html` in your web browser.

## Development

The application uses:
- **HTML5** for structure
- **Tailwind CSS** (via CDN) for styling
- **Vanilla JavaScript** for interactivity
- **LocalStorage** for demo authentication

## Demo Credentials

Since this is a demo application, any username/password combination will work for login.

## Browser Support

Works in all modern browsers that support:
- ES6 JavaScript
- CSS Grid and Flexbox
- LocalStorage API

## License

MIT License - feel free to use this project as a starting point for your own learning platform!
